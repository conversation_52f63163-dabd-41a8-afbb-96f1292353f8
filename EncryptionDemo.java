import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.TreeMap;
import org.springframework.util.DigestUtils;

public class EncryptionDemo {
    public static void main(String[] args) {
        TreeMap<String, String> params = new TreeMap<>();
        params.put("appkey", "classCard");
        
        String appSecret = "HK8gXo7WcSh4Bcs7";
        String result = buildEnc(params, appSecret);
        
        System.out.println("Encryption result: " + result);
    }
    
    public static String buildEnc(TreeMap<String,String> params, String appSecret) {
        // Original method implementation
        StringBuilder builder = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            builder.append(key+"="+value+"&");
        }
        builder.append("appSecret="+appSecret+"&");
        builder.append("time="+new SimpleDateFormat("yyyy-MM-dd").format(new Date(System.currentTimeMillis())));
        String content = builder.toString();
        System.out.println("[评价接口校验][content]=>"+content);
        return DigestUtils.md5DigestAsHex(content.getBytes());
    }
}