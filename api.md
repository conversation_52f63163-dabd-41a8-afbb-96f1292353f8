stu-evaluate-system(评价系统)
Base URLs:

正式环境: http://xspj.chaoxing.com/api/evaluate/v1
GET 班牌端获取单位下，指定方案的班级画像列表
GET /classCard/classList

请求参数
名称	位置	类型	必选	说明
fid	query	integer	是	单位id
planId	query	integer	是	方案id
enc	query	string	是	md5加密的enc
appKey	query	string	是	业务方申请的key
time	query	string	是	时间（yyyy-MM-dd）
返回示例

{
    "code": 200,
    "message": "success!",
    "data": [
        "https://xspj.chaoxing.com/pc/front/class-portrayal?fid=72204&serviceId=3095&planId=325&classId=56037344&mappId=classCard&enckey=0064b3256b25f532121b7171388dc738",
        "https://xspj.chaoxing.com/pc/front/class-portrayal?fid=72204&serviceId=3095&planId=325&classId=56036550&mappId=classCard&enckey=0e44226fa50041327e09bb97ae1b862c",
        "https://xspj.chaoxing.com/pc/front/class-portrayal?fid=72204&serviceId=3095&planId=325&classId=56028003&mappId=classCard&enckey=2e128998f701bbdc3b7e4f0a36223151",
        "https://xspj.chaoxing.com/pc/front/class-portrayal?fid=72204&serviceId=3095&planId=325&classId=56025857&mappId=classCard&enckey=b56025677b8ba68cbd6c4c5b4972b504",
        "https://xspj.chaoxing.com/pc/front/class-portrayal?fid=72204&serviceId=3095&planId=325&classId=56010355&mappId=classCard&enckey=6df4a8d254b8e4738083bed562efef45",
    ]
}
返回结果
状态码	状态码含义	说明	数据类型
200	OK	成功	int
返回数据结构
{
    "code": 状态码200成功,
    "message": "success!",
    "data": [] 班级画像页面地址集合
}