import hashlib
from datetime import datetime
from collections import OrderedDict

def build_enc(params, app_secret):
    """
    Python equivalent of the Java buildEnc method
    """
    # Build the content string
    builder = []
    
    # Add parameters in sorted order (TreeMap equivalent)
    for key in sorted(params.keys()):
        value = params[key]
        builder.append(f"{key}={value}&")
    
    # Add app secret
    builder.append(f"appSecret={app_secret}&")
    
    # Add current date in yyyy-MM-dd format
    current_date = datetime.now().strftime("%Y-%m-%d")
    builder.append(f"time={current_date}")
    
    # Join all parts
    content = "".join(builder)
    print(f"[评价接口校验][content]=>{content}")
    
    # Generate MD5 hash (equivalent to DigestUtils.md5DigestAsHex)
    md5_hash = hashlib.md5(content.encode('utf-8')).hexdigest()
    return md5_hash

def main():
    # Create parameters dictionary (equivalent to TreeMap)
    params = {"appkey": "classCard"}
    
    app_secret = "HK8gXo7WcSh4Bcs7"
    result = build_enc(params, app_secret)
    
    print(f"Encryption result: {result}")

if __name__ == "__main__":
    main()
